using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NJS.Domain.Database;
using NJS.Domain.Entities;
using NJS.Application.Dtos;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace NJSAPI.Controllers
{
    [ApiController]
    [Route("api/projects/{projectId}/plannedhours")]
    public class PlannedHoursController : ControllerBase
    {
        private readonly ProjectManagementContext _context;

        public PlannedHoursController(ProjectManagementContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Get planned hours for a project
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <returns>Planned hours data</returns>
        [HttpGet]
        public async Task<IActionResult> GetPlannedHours(int projectId)
        {
            var wbs = await _context.WorkBreakdownStructures
                .Include(w => w.Tasks)
                    .ThenInclude(t => t.PlannedHours)
                .AsNoTracking()
                .FirstOrDefaultAsync(w => w.ProjectId == projectId && w.IsActive);

            if (wbs == null)
            {
                return Ok(new { plannedHours = Array.Empty<object>() });
            }

            var plannedHours = wbs.Tasks
                .Where(t => !t.IsDeleted)
                .SelectMany(t => t.PlannedHours.Select(ph => new
                {
                    task_id = t.Id,
                    year = ph.Year,
                    monthno = ph.Month,
                    day = ph.Day?.ToString(),
                    weekno = ph.WeekNumber,
                    planned_hours = ph.PlannedHours,
                    actual_hours = ph.ActualHours
                }))
                .ToList();

            return Ok(plannedHours);
        }

        /// <summary>
        /// Update planned hours for a task
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="data">Planned hours data</param>
        /// <returns>Updated planned hours data</returns>
        [HttpPut("tasks/{taskId}/plannedhours")]
        public async Task<IActionResult> UpdatePlannedHours(
            int projectId,
            int taskId,
            [FromBody] UpdatePlannedHoursRequest data)
        {
            var task = await _context.WBSTasks
                .Include(t => t.PlannedHours)
                .Include(t => t.UserWBSTasks)
                .FirstOrDefaultAsync(t => t.Id == taskId &&
                                         t.WorkBreakdownStructure.ProjectId == projectId &&
                                         !t.IsDeleted);

            if (task == null)
            {
                return NotFound();
            }

            // Remove existing planned hours
            foreach (var existingHour in task.PlannedHours.ToList())
            {
                _context.Remove(existingHour);
            }

            // Add new planned hours
            foreach (var ph in data.PlannedHours)
            {
                // Validate planned hours not exceeding 24 hours per day
                if (ph.PlannedHours > 24)
                {
                    return BadRequest("Planned hours cannot exceed 24 hours per day");
                }

                var newPlannedHour = new WBSTaskPlannedHour
                {
                    WBSTaskId = taskId,
                    Year = ph.Year,
                    Month = ph.MonthNo,
                    Day = string.IsNullOrEmpty(ph.Day) ? null : int.Parse(ph.Day),
                    WeekNumber = ph.WeekNo,
                    PlannedHours = ph.PlannedHours,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System" // Replace with current user
                };

                _context.Add(newPlannedHour);
            }

            // Update total hours and cost in UserWBSTask
            var totalHours = data.PlannedHours.Sum(ph => ph.PlannedHours);
            var userTask = task.UserWBSTasks.FirstOrDefault();

            if (userTask != null)
            {
                userTask.TotalHours = totalHours;
                userTask.TotalCost = (decimal)totalHours * userTask.CostRate;
                userTask.UpdatedAt = DateTime.UtcNow;
                userTask.UpdatedBy = "System"; // Replace with current user
            }

            await _context.SaveChangesAsync();

            return Ok(new
            {
                planned_hours = data.PlannedHours,
                total_hours = totalHours,
                total_cost = userTask?.TotalCost ?? 0
            });
        }

        /// <summary>
        /// Add planned hours for a WBS task
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="plannedHourDto">Planned hour data</param>
        /// <returns>Created planned hour</returns>
        [HttpPost("tasks/{taskId}/planned-hours")]
        public async Task<IActionResult> AddPlannedHours(int projectId, int taskId, [FromBody] PlannedHourDto plannedHourDto)
        {
            // Validate planned hours not exceeding 24 hours per day
            if (plannedHourDto.PlannedHours > 24)
            {
                return BadRequest("Planned hours cannot exceed 24 hours per day");
            }

            var task = await _context.WBSTasks
                .Include(t => t.PlannedHours)
                .Include(t => t.WorkBreakdownStructure)
                .FirstOrDefaultAsync(t => t.Id == taskId &&
                                         t.WorkBreakdownStructure.ProjectId == projectId &&
                                         !t.IsDeleted);

            if (task == null)
            {
                return NotFound($"WBS Task with ID {taskId} not found");
            }

            var plannedHour = new WBSTaskPlannedHour
            {
                WBSTaskId = taskId,
                Year = plannedHourDto.Year.ToString(),
                Month = plannedHourDto.MonthNo,
                Day = string.IsNullOrEmpty(plannedHourDto.Day) ? null : int.Parse(plannedHourDto.Day),
                WeekNumber = plannedHourDto.WeekNo,
                PlannedHours = plannedHourDto.PlannedHours,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System" // Replace with current user
            };

            _context.WBSTaskPlannedHours.Add(plannedHour);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetPlannedHours), new { projectId }, plannedHourDto);
        }

        /// <summary>
        /// Delete planned hours for a WBS task
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="plannedHourId">Planned Hour ID</param>
        /// <returns>Success or error response</returns>
        [HttpDelete("tasks/{taskId}/planned-hours/{plannedHourId}")]
        public async Task<IActionResult> DeletePlannedHours(int projectId, int taskId, int plannedHourId)
        {
            var plannedHour = await _context.WBSTaskPlannedHours
                .Include(ph => ph.WBSTask)
                .ThenInclude(t => t.WorkBreakdownStructure)
                .FirstOrDefaultAsync(ph => ph.Id == plannedHourId &&
                                          ph.WBSTaskId == taskId &&
                                          ph.WBSTask.WorkBreakdownStructure.ProjectId == projectId);

            if (plannedHour == null)
            {
                return BadRequest($"Invalid planned hour ID {plannedHourId} for task {taskId}");
            }

            _context.WBSTaskPlannedHours.Remove(plannedHour);
            await _context.SaveChangesAsync();

            return Ok(new { message = "Planned hour deleted successfully" });
        }
    }

    public class PlannedHourData
    {
        public string Year { get; set; }
        public int MonthNo { get; set; } // Changed from string Month to int MonthNo
        public string Day { get; set; }
        public int? WeekNo { get; set; }
        public double PlannedHours { get; set; }
    }

    public class UpdatePlannedHoursRequest
    {
        public PlannedHourData[] PlannedHours { get; set; }
    }
}
